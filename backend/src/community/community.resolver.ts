import {
  Resolver,
  Query,
  Args,
  Int,
  ID,
  Mutation,
  Parent,
  ResolveField,
} from '@nestjs/graphql';
import { CommunityService } from './community.service';
import { Thread, Comment } from './dto/community.models';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from 'src/users/user.entity';

@Resolver(() => Thread)
export class ThreadResolver {
  constructor(private readonly communityService: CommunityService) {}

  @Query(() => [Thread], { name: 'threads' })
  async getThreads(
    @CurrentUser() user: User,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 })
    limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 })
    offset: number,
    @Args('communityId', { type: () => String, nullable: true })
    communityId?: string,
  ) {
    // 1. Fetch the main documents (threads)
    const threads = await this.communityService.getThreads({
      limit,
      offset,
      communityId,
    });
    if (!user || threads.length === 0) {
      return threads;
    }

    // 2. Collect IDs for batch fetching
    const threadIds = threads.map((t) => t._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions =
      await this.communityService.findUserReactionsForThreads(
        threadIds,
        user.id,
      );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    threads.forEach((thread) => {
      thread.myReaction = reactionMap.get(thread._id.toHexString()) || null;
    });

    return threads;
  }

  @Query(() => Thread, { name: 'thread' })
  async getThreadById(
    @CurrentUser() user: User,
    @Args('id', { type: () => ID }) id: string,
  ) {
    const thread = await this.communityService.getThreadById(id);
    if (!user || !thread) {
      return thread;
    }
    const reaction = await this.communityService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = reaction ? reaction.reactionType : null;
    return thread;
  }

  @ResolveField('myReaction', () => String, { nullable: true })
  myReaction(@Parent() thread: Thread): string | null {
    // This will now be resolved by the parent queries (getThreads, getThreadById)
    // and attached directly. If it's not there, it's null.
    return thread.myReaction || null;
  }

  @Query(() => [Comment], { name: 'comments' })
  async getComments(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 20 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    // 1. Fetch the main documents (comments)
    const comments = await this.communityService.getCommentsForThread(threadId, { limit, offset });
    if (!user || comments.length === 0) {
      return comments;
    }

    // 2. Collect IDs for batch fetching
    const commentIds = comments.map((c) => c._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions = await this.communityService.findUserReactionsForComments(
      commentIds,
      user.id,
    );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    comments.forEach((comment) => {
      comment.myReaction = reactionMap.get(comment._id.toHexString()) || null;
    });

    return comments;
  }

  @Query(() => [Comment], { name: 'replies' })
  async getReplies(
    @CurrentUser() user: User,
    @Args('parentCommentId', { type: () => ID }) parentCommentId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    // 1. Fetch the main documents (replies)
    const replies = await this.communityService.getRepliesForComment(parentCommentId, { limit, offset });
    if (!user || replies.length === 0) {
      return replies;
    }

    // 2. Collect IDs for batch fetching
    const replyIds = replies.map((r) => r._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions = await this.communityService.findUserReactionsForComments(
      replyIds,
      user.id,
    );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    replies.forEach((reply) => {
      reply.myReaction = reactionMap.get(reply._id.toHexString()) || null;
    });

    return replies;
  }

  @Mutation(() => Thread, { name: 'addReaction' })
  async addReaction(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('reaction', { type: () => String }) reaction: string,
  ) {
    const thread = await this.communityService.addReactionToThread(
      threadId,
      user.id,
      reaction,
    );

    // After mutation, we need to manually add the user's reaction
    // to the returned object so the frontend gets the updated state.
    const userReaction = await this.communityService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = userReaction ? userReaction.reactionType : null;
    return thread;
  }
}

@Resolver(() => Comment)
export class CommentResolver {
  constructor(private readonly communityService: CommunityService) {}

  @ResolveField('myReaction', () => String, { nullable: true })
  myReaction(@Parent() comment: Comment): string | null {
    // This will now be resolved by the parent queries (getComments, getReplies)
    // and attached directly. If it's not there, it's null.
    return comment.myReaction || null;
  }

  @Mutation(() => Comment, { name: 'addCommentReaction' })
  async addCommentReaction(
    @CurrentUser() user: User,
    @Args('commentId', { type: () => ID }) commentId: string,
    @Args('reaction', { type: () => String }) reaction: string,
  ) {
    const comment = await this.communityService.addReactionToComment(
      commentId,
      user.id,
      reaction,
    );

    // After mutation, we need to manually add the user's reaction
    // to the returned object so the frontend gets the updated state.
    const userReaction = await this.communityService.findUserReactionForComment(
      comment._id,
      user.id,
    );
    comment.myReaction = userReaction ? userReaction.reactionType : null;
    return comment;
  }
}
