import { Injectable, NotFoundException, OnModuleInit, ServiceUnavailableException, Logger } from '@nestjs/common';
import { MongoService } from '../mongo/mongo.service';
import { Collection, ObjectId } from 'mongodb';
import { Thread, Comment, Reaction } from './dto/community.models';

@Injectable()
export class CommunityService implements OnModuleInit {

  private threadsCollection: Collection<Thread> | null = null;
  private commentsCollection: Collection<Comment> | null = null;
  private reactionsCollection: Collection<Reaction> | null = null;
  private readonly logger = new Logger(CommunityService.name);
  

  constructor(private readonly mongoService: MongoService) {}

  private environment = process.env.NODE_ENV;
  //private mongoDB = this.environment === 'development' ? 'chronicare-forum-dev' : 'chronicare-forum-prod';
  private mongoDB = 'chronicare-forum-dev';

  onModuleInit() {
    this.logger.log('Mongo DB: ', this.mongoDB);
    const db = this.mongoService.getDatabase(this.mongoDB);
    if (db) {
      this.threadsCollection = db.collection<Thread>('Threads');
      this.commentsCollection = db.collection<Comment>('Comments');
      this.reactionsCollection = db.collection<Reaction>('Reactions');
    } else {
      this.logger.warn('Database not available. CommunityService will not be functional.'); 
    }
  }

  private checkDbConnection() {
    if (!this.threadsCollection || !this.commentsCollection || !this.reactionsCollection) {
      throw new ServiceUnavailableException('Database service is not available.');
    }
  }

  async getThreads({ limit = 10, offset = 0, communityId }: { limit?: number; offset?: number; communityId?: string }): Promise<Thread[]> {
    this.checkDbConnection();
    const query = communityId ? { communityId } : {};
    return this.threadsCollection!
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Thread[]>;
  }

  async getThreadById(id: string): Promise<Thread> {
    this.checkDbConnection();
    if (!ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    const thread = await this.threadsCollection!.findOne({ _id: new ObjectId(id) });
    if (!thread) {
      throw new NotFoundException(`Thread with ID ${id} not found.`);
    }
    return thread as Thread;
  }

  async getCommentsForThread(threadId: string, { limit = 50, offset = 0 }): Promise<Comment[]> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    return this.commentsCollection!
      .find({ 
          threadId: new ObjectId(threadId),
      })
      .sort({ createdAt: 1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Comment[]>;
  }

  async getRepliesForComment(parentCommentId: string, { limit = 10, offset = 0 }): Promise<Comment[]> {
    this.checkDbConnection();
    if (!ObjectId.isValid(parentCommentId)) {
        throw new NotFoundException('Invalid comment ID format.');
    }
    return this.commentsCollection!
      .find({ parentCommentId: new ObjectId(parentCommentId) })
      .sort({ createdAt: 1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Comment[]>;
  }

  async addReactionToThread(
    threadId: string,
    userId: string,
    reactionType: string,
  ): Promise<Thread> {
    this.checkDbConnection();
    const threadObjectId = new ObjectId(threadId);
    
    const client = this.mongoService.getClient();
    if (!client) {
      throw new ServiceUnavailableException('Database client is not available.');
    }
    const session = client.startSession();

    try {
      await session.withTransaction(async () => {
        // 1. Validate Thread exists
        const thread = await this.threadsCollection!.findOne({ _id: threadObjectId }, { session });
        if (!thread) {
          throw new NotFoundException(`Thread with ID ${threadId} not found.`);
        }

        // 2. Find any existing reaction by this user on this thread
        const existingReaction = await this.reactionsCollection!.findOne({
          documentId: threadObjectId,
          reactorId: userId,
        }, { session });

        let isTogglingOff = false;

        // 3. If a reaction already exists, handle removal/update
        if (existingReaction) {
          const oldReactionType = existingReaction.reactionType;
          isTogglingOff = oldReactionType === reactionType;

          // Decrement the count of the old reaction
          await this.threadsCollection!.updateOne(
            { _id: threadObjectId },
            { $inc: { [`reactionCounts.${oldReactionType}`]: -1 } },
            { session }
          );
          
          // Remove the old reaction document
          await this.reactionsCollection!.deleteOne({ _id: existingReaction._id }, { session });
          this.logger.log(`User ${userId} removed reaction ${oldReactionType} from thread ${threadId}`);
        }
        
        // 4. If the user is not just toggling off, add the new reaction
        if (!isTogglingOff) {
          // Increment the count of the new reaction
          await this.threadsCollection!.updateOne(
            { _id: threadObjectId },
            { $inc: { [`reactionCounts.${reactionType}`]: 1 } },
            { session }
          );

          // Insert the new reaction document
          await this.reactionsCollection!.insertOne({
            documentId: threadObjectId,
            documentType: 'Thread',
            reactorId: userId,
            reactionType: reactionType,
            createdAt: new Date(),
            updatedAt: new Date(),
          } as Reaction, { session });
          this.logger.log(`User ${userId} added reaction ${reactionType} to thread ${threadId}`);
        }
      });
    } finally {
      await session.endSession();
    }

    // 5. Return the updated thread state
    return this.getThreadById(threadId);
  }

  async findUserReactionForThread(
    threadId: ObjectId,
    userId: string,
  ): Promise<Reaction | null> {
    this.checkDbConnection();
    return this.reactionsCollection!.findOne({
      documentId: threadId,
      reactorId: userId,
      documentType: 'Thread',
    });
  }

  async findUserReactionsForThreads(
    threadIds: ObjectId[],
    userId: string,
  ): Promise<Reaction[]> {
    this.checkDbConnection();
    if (threadIds.length === 0) {
      return [];
    }
    return this.reactionsCollection!
      .find({
        documentId: { $in: threadIds },
        reactorId: userId,
        documentType: 'Thread',
      })
      .toArray();
  }
}
