{"name": "chronicare", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@expo-google-fonts/inter": "^0.3.0", "@expo-google-fonts/merriweather": "^0.3.0", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.5", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@rneui/base": "^0.0.0-edge.2", "@rneui/themed": "^0.0.0-edge.2", "expo": "53.0.12", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-mesh-gradient": "~0.3.4", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-updates": "~0.28.15", "expo-web-browser": "~14.2.0", "graphql": "^16.11.0", "i18n-iso-countries": "^7.14.0", "lucide-react-native": "^0.511.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.5", "expo-device": "~7.1.4", "expo-network": "~7.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/lodash": "^4.17.19", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}