import { useMutation, useApolloClient, gql } from '@apollo/client';
import { THREAD_FRAGMENT, ReactionCount, Thread } from '../graphql/fragments';
import { GET_THREADS } from '../graphql/queries';
import { useState, useCallback, useRef, useEffect } from 'react';

const ADD_REACTION = gql`
  mutation AddReaction($threadId: ID!, $reaction: String!) {
    addReaction(threadId: $threadId, reaction: $reaction) {
      _id
      reactionCounts {
        love
        withYou
        funny
        insightful
        poop
      }
      myReaction
    }
  }
`;

type ReactionType = keyof ReactionCount;

interface AddReactionResult {
  addReaction: {
    _id: string;
    reactionCounts: ReactionCount;
    myReaction: string | null;
  };
}

export const useThreadReactions = (threadId: string) => {
  const client = useApolloClient();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Optimized cache reading using readFragment instead of full query
  const getCurrentThread = useCallback((): Thread | null => {
    try {
      return client.readFragment({
        id: client.cache.identify({ __typename: 'Thread', _id: threadId }),
        fragment: THREAD_FRAGMENT,
      }) as Thread | null;
    } catch {
      return null;
    }
  }, [client, threadId]);

  // Retry mechanism with exponential backoff
  const retryReaction = useCallback((reaction: ReactionType, retryCount = 0) => {
    if (retryCount >= 3) {
      setErrorMessage('Unable to add reaction. Please check your connection and try again.');
      return;
    }

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    retryTimeoutRef.current = setTimeout(() => {
      addReactionMutation({
        variables: { threadId, reaction },
      }).catch(() => {
        retryReaction(reaction, retryCount + 1);
      });
    }, Math.pow(2, retryCount) * 1000); // Exponential backoff: 1s, 2s, 4s
  }, [threadId]);

  const [addReactionMutation, { loading, error }] = useMutation<AddReactionResult>(ADD_REACTION, {
    optimisticResponse: (variables, { IGNORE }) => {
      const thread = getCurrentThread();

      // Skip optimistic update if thread not in cache - let Apollo handle normally
      if (!thread) {
        return IGNORE;
      }

      const newReaction = variables.reaction as ReactionType;
      const oldReaction = thread.myReaction as ReactionType | null;
      const newCounts = { ...thread.reactionCounts };

      if (oldReaction) {
        // Decrement the old reaction count
        newCounts[oldReaction] = Math.max(0, (newCounts[oldReaction] || 1) - 1);
      }

      // If the new reaction is different from the old one, increment it
      if (oldReaction !== newReaction) {
        newCounts[newReaction] = (newCounts[newReaction] || 0) + 1;
      }

      return {
        addReaction: {
          _id: variables.threadId,
          reactionCounts: newCounts,
          myReaction: oldReaction === newReaction ? null : newReaction,
        },
      };
    },
    update: (cache, { data }) => {
      if (!data?.addReaction) return;

      // Update the specific thread fragment in cache
      cache.modify({
        id: cache.identify({ __typename: 'Thread', _id: threadId }),
        fields: {
          reactionCounts: () => data.addReaction.reactionCounts,
          myReaction: () => data.addReaction.myReaction,
        },
      });
    },
    // Refetch all GET_THREADS queries to ensure consistency across all views
    refetchQueries: ['GetThreads'],
    onCompleted: () => {
      // Clear error message on successful completion
      setErrorMessage(null);
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    },
    onError: (apolloError) => {
      console.error('Failed to add reaction:', apolloError);

      // Set user-friendly error message
      if (apolloError.networkError) {
        setErrorMessage('Network error. Please check your connection.');
      } else if (apolloError.graphQLErrors?.length > 0) {
        setErrorMessage('Unable to add reaction. Please try again.');
      } else {
        setErrorMessage('Something went wrong. Please try again.');
      }
    },
  });

  // Non-debounced reaction function for immediate optimistic updates
  const addReaction = useCallback(async (reaction: ReactionType) => {
    if (loading) return; // Prevent concurrent requests

    // Clear any previous error messages
    setErrorMessage(null);

    try {
      await addReactionMutation({
        variables: { threadId, reaction },
      });
    } catch (err) {
      // Error is handled by onError callback
      console.error('Reaction failed:', err);
    }
  }, [addReactionMutation, loading, threadId, setErrorMessage]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    addReaction,
    loading,
    error,
    errorMessage,
    getCurrentThread,
  };
};