import { View, Text, StyleSheet, Pressable, Modal, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { useThreadOptimized } from '@/scr/hooks/useThread';
import { useThreadReactions } from '@/scr/hooks/useThreadReactions';
import { useReactionDisplay, iconMapping } from '@/scr/hooks/useReactionDisplay';
import ReactionIcon from './ReactionIcon';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

interface ReactionsProps {
  threadId: string;
}

const Reactions = ({ threadId }: ReactionsProps) => {
  const { theme } = useTheme();
  const thread = useThreadOptimized(threadId);
  const {
    addReaction,
    errorMessage,
  } = useThreadReactions(threadId);
  const [modalVisible, setModalVisible] = useState(false);

  // Use the custom hook for reaction display logic
  const { totalReactions, displayedReactions } = useReactionDisplay(thread?.reactionCounts || null);

  // Show error alert when errorMessage changes
  React.useEffect(() => {
    if (errorMessage) {
      Alert.alert(
        'Reaction Error',
        errorMessage,
        [
          {
            text: 'OK',
            onPress: () => {
              // Error message will be cleared by the hook
            },
          },
        ]
      );
    }
  }, [errorMessage]);

  const myReaction = thread?.myReaction as ReactionType | null;

  const handleQuickReaction = useCallback(() => {
    // Add haptic feedback for quick reaction
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // If a reaction is already selected, toggle it. Otherwise, default to 'love'.
    const reactionToToggle = myReaction || 'love';
    addReaction(reactionToToggle);
  }, [addReaction, myReaction]);

  const handleSelectReaction = useCallback((reaction: ReactionType) => {
    // Add haptic feedback for reaction selection
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    addReaction(reaction);
    setModalVisible(false);
  }, [addReaction]);

  const handleLongPress = useCallback(() => {
    // Add haptic feedback for long press
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    setModalVisible(true);
  }, []);

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flexDirection: 'row',
          alignItems: 'center',
          borderRadius: 20,
          borderWidth: 1,
          borderColor: theme.colors.Text.text100,
          paddingVertical: theme.spacing.spacing.s1,
          paddingHorizontal: theme.spacing.spacing.s1,
          alignSelf: 'flex-start',
        },
        iconsContainer: {
          flexDirection: 'row',
          alignItems: 'center',
        },
        overlappingIcon: {
          marginLeft: -8,
        },
        totalText: {
          ...theme.textVariants.text('sm', 'regular'),
          color: theme.colors.Text.text900,
          marginLeft: theme.spacing.spacing.s2,
        },
        modalContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        },
        modalContent: {
          flexDirection: 'row',
          backgroundColor: theme.colors.Background.background100,
          borderRadius: 30,
          padding: theme.spacing.spacing.s4,
          elevation: 5,
          gap: theme.spacing.spacing.s3,
        },
      }),
    [theme],
  );

  return (
    <>
      <Pressable
        style={styles.container}
        onPress={handleQuickReaction}
        onLongPress={handleLongPress}
        accessibilityRole="button">
        <View style={styles.iconsContainer}>
          {displayedReactions.map(({ key }, index) => (
            <View
              key={key}
              style={[
                index > 0 && styles.overlappingIcon,
                { zIndex: displayedReactions.length - index },
              ]}>
              <ReactionIcon 
                reactionType={key}
                isSelected={myReaction === key}
              />
            </View>
          ))}
        </View>
        <Text style={styles.totalText}>{totalReactions}</Text>
      </Pressable>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        accessibilityViewIsModal={true}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
          accessibilityRole="button">
          <View style={styles.modalContent}>
            {Object.entries(iconMapping).map(([key]) => (
              <Pressable
                key={key}
                onPress={() => handleSelectReaction(key as ReactionType)}
                accessibilityRole="button"
                style={({ pressed }) => ({
                  opacity: pressed ? 0.7 : 1,
                })}>
                  <ReactionIcon
                    reactionType={key as ReactionType}
                    isSelected={myReaction === key}
                    size={32}
                  />
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default React.memo(Reactions);
