import { View, Text, StyleSheet, Pressable, Modal, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { useCommentReactions } from '@/scr/hooks/useCommentReactions';
import { useReactionDisplay } from '@/scr/hooks/useReactionDisplay';
import ReactionIcon from './ReactionIcon';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

interface Comment {
  _id: string;
  reactionCounts: ReactionCount;
  myReaction: string | null;
}

interface CommentReactionsProps {
  comment: Comment;
}

const CommentReactions = ({ comment }: CommentReactionsProps) => {
  const { theme } = useTheme();
  const {
    addReaction,
    errorMessage,
  } = useCommentReactions(comment._id);
  const [modalVisible, setModalVisible] = useState(false);

  // Use the custom hook for reaction display logic
  const { totalReactions, displayedReactions, iconMapping } = useReactionDisplay(comment?.reactionCounts || null);

  // Show error alert when errorMessage changes
  React.useEffect(() => {
    if (errorMessage) {
      Alert.alert(
        'Reaction Error',
        errorMessage,
        [
          {
            text: 'OK',
            onPress: () => {
              // Error message will be cleared by the hook
            },
          },
        ]
      );
    }
  }, [errorMessage]);

  const myReaction = comment?.myReaction as ReactionType | null;

  const handleQuickReaction = useCallback(() => {
    // Add haptic feedback for quick reaction
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // If a reaction is already selected, toggle it. Otherwise, default to 'love'.
    const reactionToToggle = myReaction || 'love';
    addReaction(reactionToToggle);
  }, [addReaction, myReaction]);

  const handleSelectReaction = useCallback((reaction: ReactionType) => {
    // Add haptic feedback for reaction selection
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    addReaction(reaction);
    setModalVisible(false);
  }, [addReaction]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.Background.background0,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.Text.text100,
      paddingVertical: theme.spacing.spacing.s1,
      paddingHorizontal: theme.spacing.spacing.s1,
      alignSelf: 'flex-start',
    },
    reactionsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.spacing.s2,
    },
    reactionItem: {
      marginRight: theme.spacing.spacing.s1,
    },
    totalText: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text900,
    },
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      flexDirection: 'row',
      backgroundColor: theme.colors.Background.background0,
      borderRadius: 25,
      paddingHorizontal: theme.spacing.spacing.s3,
      paddingVertical: theme.spacing.spacing.s2,
      gap: theme.spacing.spacing.s2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
  }), [theme]);

  if (totalReactions === 0) {
    return (
      <>
        <Pressable
          onPress={handleQuickReaction}
          onLongPress={() => setModalVisible(true)}
          style={styles.container}
          accessibilityRole="button"
          accessibilityLabel="Add reaction">
          <View style={styles.reactionsContainer}>
            <ReactionIcon
              reactionType="love"
              isSelected={false}
              size={20}
            />
          </View>
          <Text style={styles.totalText}>0</Text>
        </Pressable>
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
          accessibilityViewIsModal={true}>
          <TouchableOpacity
            style={styles.modalContainer}
            activeOpacity={1}
            onPressOut={() => setModalVisible(false)}
            accessibilityRole="button">
            <View style={styles.modalContent}>
              {Object.entries(iconMapping).map(([key]) => (
                <Pressable
                  key={key}
                  onPress={() => handleSelectReaction(key as ReactionType)}
                  accessibilityRole="button"
                  style={({ pressed }) => ({
                    opacity: pressed ? 0.7 : 1,
                  })}>
                    <ReactionIcon
                      reactionType={key as ReactionType}
                      isSelected={myReaction === key}
                      size={32}
                    />
                </Pressable>
              ))}
            </View>
          </TouchableOpacity>
        </Modal>
      </>
    );
  }

  return (
    <>
      <Pressable
        onPress={handleQuickReaction}
        onLongPress={() => setModalVisible(true)}
        style={styles.container}
        accessibilityRole="button"
        accessibilityLabel={`${totalReactions} reactions`}>
        <View style={styles.reactionsContainer}>
          {displayedReactions.map((reaction, index) => (
            <View key={reaction.key} style={styles.reactionItem}>
              <ReactionIcon
                reactionType={reaction.key}
                isSelected={myReaction === reaction.key}
                size={20}
              />
            </View>
          ))}
        </View>
        <Text style={styles.totalText}>{totalReactions}</Text>
      </Pressable>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        accessibilityViewIsModal={true}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
          accessibilityRole="button">
          <View style={styles.modalContent}>
            {Object.entries(iconMapping).map(([key]) => (
              <Pressable
                key={key}
                onPress={() => handleSelectReaction(key as ReactionType)}
                accessibilityRole="button"
                style={({ pressed }) => ({
                  opacity: pressed ? 0.7 : 1,
                })}>
                  <ReactionIcon
                    reactionType={key as ReactionType}
                    isSelected={myReaction === key}
                    size={32}
                  />
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default CommentReactions;
